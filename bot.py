import asyncio
import json
import logging
from datetime import datetime
from telegram import Update, WebAppInfo, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, filters, CallbackQueryHandler
import aiomysql

# ⚠️ إعدادات مهمة - يجب تعديلها
TOKEN = "6700646753:AAG8wb5t7cqmYbkyxjhoWUTq-r4m-Z4Fqqk"
WEB_APP_URL = "https://radiant-cascaron-5a03bb.netlify.app/"  # رابط الخادم المحلي
ADMIN_IDS = [1856344728]  # معرفات المديرين (للإشعارات فقط - البوت متاح للجميع)

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# إعداد الاتصال بقاعدة بيانات MySQL
async def get_db_connection():
    return await aiomysql.connect(
        host='localhost',  # عدلها إذا كان لديك إعداد مختلف
        user='root',       # اسم المستخدم
        password='',       # كلمة المرور
        db='matjer_bot',   # اسم قاعدة البيانات
        autocommit=True
    )

async def ensure_user_exists(user):
    """التأكد من وجود المستخدم في قاعدة البيانات"""
    try:
        logger.info(f"محاولة التأكد من وجود المستخدم: {user.id} - {user.first_name}")
        conn = await get_db_connection()
        async with conn.cursor() as cur:
            # التحقق من وجود المستخدم أولاً
            await cur.execute('SELECT id FROM users WHERE id = %s', (user.id,))
            existing_user = await cur.fetchone()

            if existing_user:
                logger.info(f"المستخدم موجود بالفعل: {user.id}")
            else:
                # إدراج المستخدم الجديد
                await cur.execute('''
                    INSERT INTO users (id, name, username, join_date)
                    VALUES (%s, %s, %s, %s)
                ''', (user.id, user.first_name or 'مستخدم', user.username, datetime.now()))
                logger.info(f"تم إضافة مستخدم جديد: {user.id} - {user.first_name}")
        conn.close()
    except Exception as e:
        logger.error(f"خطأ في إنشاء المستخدم {user.id}: {e}")
        # لا نرفع الخطأ لتجنب توقف البوت
        pass

# إنشاء الجداول إذا لم تكن موجودة
async def init_db():
    conn = await get_db_connection()
    async with conn.cursor() as cur:
        # جدول المستخدمين
        await cur.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id BIGINT PRIMARY KEY,
                name VARCHAR(255),
                username VARCHAR(255),
                join_date DATETIME
            )
        ''')

        # جدول الفئات
        await cur.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                image_url VARCHAR(500),
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول المنتجات
        await cur.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                price DECIMAL(10,2) NOT NULL,
                image_url VARCHAR(500),
                category_id INT,
                stock_quantity INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
        ''')

        # جدول الطلبات
        await cur.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id BIGINT,
                items TEXT,
                total DECIMAL(10,2),
                status VARCHAR(50) DEFAULT 'pending',
                payment_method VARCHAR(50),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                delivery_info TEXT,
                notes TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')

        # جدول تفاصيل الطلبات
        await cur.execute('''
            CREATE TABLE IF NOT EXISTS order_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT,
                product_id INT,
                quantity INT NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                FOREIGN KEY (order_id) REFERENCES orders(id),
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        ''')

        # جدول التقييمات
        await cur.execute('''
            CREATE TABLE IF NOT EXISTS reviews (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id BIGINT,
                product_id INT,
                order_id INT,
                rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
                comment TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (product_id) REFERENCES products(id),
                FOREIGN KEY (order_id) REFERENCES orders(id),
                UNIQUE KEY unique_user_product_order (user_id, product_id, order_id)
            )
        ''')

        # جدول الإشعارات
        await cur.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id BIGINT,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                type VARCHAR(50) DEFAULT 'info',
                is_read BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')

        # إدراج بيانات تجريبية للفئات
        await cur.execute('SELECT COUNT(*) FROM categories')
        count = (await cur.fetchone())[0]
        if count == 0:
            categories_data = [
                ('الإلكترونيات', 'أجهزة إلكترونية متنوعة', 'https://via.placeholder.com/300x200?text=Electronics'),
                ('الملابس', 'ملابس رجالية ونسائية', 'https://via.placeholder.com/300x200?text=Clothes'),
                ('المنزل والحديقة', 'أدوات منزلية ومستلزمات الحديقة', 'https://via.placeholder.com/300x200?text=Home'),
                ('الكتب', 'كتب متنوعة في جميع المجالات', 'https://via.placeholder.com/300x200?text=Books'),
                ('الرياضة', 'معدات رياضية ولياقة بدنية', 'https://via.placeholder.com/300x200?text=Sports')
            ]
            for cat in categories_data:
                await cur.execute('INSERT INTO categories (name, description, image_url) VALUES (%s, %s, %s)', cat)

        # إدراج بيانات تجريبية للمنتجات
        await cur.execute('SELECT COUNT(*) FROM products')
        count = (await cur.fetchone())[0]
        if count == 0:
            products_data = [
                ('هاتف ذكي', 'هاتف ذكي بمواصفات عالية', 2500.00, 'https://via.placeholder.com/300x300?text=Phone', 1, 50),
                ('لابتوب', 'لابتوب للألعاب والعمل', 15000.00, 'https://via.placeholder.com/300x300?text=Laptop', 1, 20),
                ('قميص قطني', 'قميص قطني مريح', 150.00, 'https://via.placeholder.com/300x300?text=Shirt', 2, 100),
                ('بنطلون جينز', 'بنطلون جينز عالي الجودة', 300.00, 'https://via.placeholder.com/300x300?text=Jeans', 2, 75),
                ('مصباح LED', 'مصباح LED موفر للطاقة', 80.00, 'https://via.placeholder.com/300x300?text=LED', 3, 200),
                ('كتاب البرمجة', 'كتاب تعلم البرمجة للمبتدئين', 120.00, 'https://via.placeholder.com/300x300?text=Book', 4, 30),
                ('كرة قدم', 'كرة قدم احترافية', 200.00, 'https://via.placeholder.com/300x300?text=Ball', 5, 40)
            ]
            for prod in products_data:
                await cur.execute('INSERT INTO products (name, description, price, image_url, category_id, stock_quantity) VALUES (%s, %s, %s, %s, %s, %s)', prod)

    conn.close()

# قواعد البيانات البسيطة
orders_db = {}
users_db = {}

async def start(update: Update, context):
    """بدء البوت"""
    user = update.effective_user
    
    # التأكد من وجود المستخدم في قاعدة البيانات
    await ensure_user_exists(user)
    
    # إنشاء الأزرار - متاحة لجميع المستخدمين
    keyboard = [
        [InlineKeyboardButton("🛍️ فتح المتجر", web_app=WebAppInfo(url=WEB_APP_URL))],
        [InlineKeyboardButton("📋 طلباتي", callback_data="my_orders")],
        [InlineKeyboardButton("⚙️ لوحة الإدارة", callback_data="admin_panel")],
        [InlineKeyboardButton("📦 إدارة المنتجات", callback_data="manage_products")]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)
    
    welcome_message = f"""
🎉 أهلاً وسهلاً {user.first_name}!

مرحباً بك في متجرنا الإلكتروني 🛍️

✨ الميزات المتاحة:
- 🛍️ فتح المتجر للتسوق
- 📋 متابعة طلباتك
- ⚙️ لوحة الإدارة لإدارة المتجر
- 📦 إدارة المنتجات والفئات

🚀 يمكنك الآن إدارة المتجر وتلقي الطلبات!
    """
    
    await update.message.reply_text(welcome_message, reply_markup=reply_markup)

async def add_product_command(update: Update, context):
    """إضافة منتج جديد - متاح لجميع المستخدمين"""
    if not context.args:
        await update.message.reply_text("""
📝 <b>لإضافة منتج جديد، استخدم التنسيق التالي:</b>

<code>/add_product اسم_المنتج|الوصف|السعر|رقم_الفئة|الكمية</code>

<b>مثال:</b>
<code>/add_product هاتف جديد|هاتف ذكي متطور|1500|1|25</code>

<b>أرقام الفئات المتاحة:</b>
1 - الإلكترونيات
2 - الملابس
3 - المنزل والحديقة
4 - الكتب
5 - الرياضة
        """, parse_mode='HTML')
        return

    try:
        # دمج جميع المعاملات في نص واحد
        product_data = ' '.join(context.args)
        parts = product_data.split('|')

        if len(parts) != 5:
            await update.message.reply_text("❌ تنسيق خاطئ! يجب أن تكون البيانات: اسم|وصف|سعر|فئة|كمية")
            return

        name = parts[0].strip()
        description = parts[1].strip()
        price = float(parts[2].strip())
        category_id = int(parts[3].strip())
        stock = int(parts[4].strip())

        # التحقق من وجود الفئة
        conn = await get_db_connection()
        async with conn.cursor() as cur:
            await cur.execute('SELECT id FROM categories WHERE id=%s', (category_id,))
            if not await cur.fetchone():
                await update.message.reply_text("❌ رقم الفئة غير صحيح!")
                conn.close()
                return

            # إضافة المنتج
            await cur.execute('''
                INSERT INTO products (name, description, price, category_id, stock_quantity)
                VALUES (%s, %s, %s, %s, %s)
            ''', (name, description, price, category_id, stock))

            product_id = cur.lastrowid
        conn.close()

        await update.message.reply_text(f"""
✅ <b>تم إضافة المنتج بنجاح!</b>

🏷️ <b>رقم المنتج:</b> #{product_id}
📦 <b>الاسم:</b> {name}
📝 <b>الوصف:</b> {description}
💰 <b>السعر:</b> {price} جنيه
📂 <b>الفئة:</b> {category_id}
📊 <b>الكمية:</b> {stock}
        """, parse_mode='HTML')

    except ValueError:
        await update.message.reply_text("❌ خطأ في البيانات! تأكد من أن السعر والفئة والكمية أرقام صحيحة")
    except Exception as e:
        logger.error(f"خطأ في إضافة المنتج: {e}")
        await update.message.reply_text("❌ حدث خطأ في إضافة المنتج")

async def handle_web_app_data(update: Update, context):
    """معالجة البيانات من Web App"""
    logger.info("🚨🚨🚨 تم استدعاء معالج Web App Data!")
    try:
        user = update.effective_user
        logger.info(f"🌐 استلام بيانات من Web App من المستخدم: {user.id} - {user.first_name}")
        logger.info(f"🔍 فحص الرسالة: {update.effective_message}")
        logger.info(f"🔍 فحص web_app_data: {update.effective_message.web_app_data if update.effective_message else 'لا توجد رسالة'}")

        # التحقق من وجود web_app_data
        if not update.effective_message or not update.effective_message.web_app_data:
            logger.error("❌ لا توجد بيانات Web App في الرسالة!")
            if update.effective_message:
                await update.effective_message.reply_text("❌ خطأ: لا توجد بيانات Web App")
            return

        # قراءة البيانات المرسلة من التطبيق
        web_app_data = update.effective_message.web_app_data.data
        logger.info(f"📄 البيانات المستلمة: {web_app_data}")

        if not web_app_data:
            logger.error("❌ البيانات المستلمة فارغة!")
            await update.effective_message.reply_text("❌ خطأ: البيانات المستلمة فارغة")
            return

        data = json.loads(web_app_data)
        logger.info(f"📊 البيانات بعد التحويل: {data}")
        
        # التحقق من وجود البيانات المطلوبة
        if not data:
            logger.error("لا توجد بيانات في الطلب")
            await update.effective_message.reply_text("❌ خطأ: لا توجد بيانات في الطلب")
            return
        
        # معالجة أنواع مختلفة من البيانات
        logger.info(f"🔍 تحليل نوع البيانات...")
        if data.get('type') == 'new_order' or 'items' in data:
            logger.info(f"✅ تم التعرف على البيانات كطلب جديد")
            await process_new_order(update, context, data)
        else:
            logger.warning(f"⚠️ نوع بيانات غير معروف: {data}")
            # معالجة البيانات كطلب مباشر إذا كانت تحتوي على منتجات
            if 'items' in data or 'total' in data:
                logger.info(f"🔄 محاولة معالجة البيانات كطلب مباشر")
                await process_new_order(update, context, data)
            else:
                logger.error(f"❌ نوع البيانات غير مدعوم: {data}")
                await update.effective_message.reply_text("❌ نوع البيانات غير مدعوم")
            
    except json.JSONDecodeError as e:
        logger.error(f"خطأ في قراءة JSON: {e}")
        await update.effective_message.reply_text("❌ خطأ في معالجة البيانات")
    except Exception as e:
        logger.error(f"❌ خطأ عام في معالجة البيانات: {e}", exc_info=True)
        await update.effective_message.reply_text("❌ حدث خطأ، يرجى المحاولة مرة أخرى")

async def handle_all_messages(update: Update, context):
    """معالج عام لجميع الرسائل لتتبع ما يحدث"""
    try:
        user = update.effective_user
        message = update.effective_message

        logger.info(f"📨 رسالة جديدة من {user.id} - {user.first_name}")
        logger.info(f"نوع الرسالة: {type(message)}")
        logger.info(f"🔍 فحص تفصيلي للرسالة:")
        logger.info(f"   - message.web_app_data: {message.web_app_data}")
        logger.info(f"   - message.text: {message.text}")
        logger.info(f"   - message.photo: {message.photo}")
        logger.info(f"   - message.document: {message.document}")
        logger.info(f"   - message.content_type: {message.content_type}")

        # فحص إذا كانت الرسالة تحتوي على web_app_data
        if message.web_app_data:
            logger.info("🌐 الرسالة تحتوي على web_app_data - سيتم معالجتها بواسطة المعالج المخصص")
            logger.info(f"� بيانات Web App: {message.web_app_data.data}")
            return

        if message.text:
            logger.info(f"📝 رسالة نصية: {message.text}")
        else:
            logger.info("❓ نوع رسالة غير معروف")
            logger.info(f"تفاصيل الرسالة: {message}")

        # التأكد من وجود المستخدم في قاعدة البيانات
        await ensure_user_exists(user)

    except Exception as e:
        logger.error(f"خطأ في معالج الرسائل العام: {e}", exc_info=True)

async def process_new_order(update: Update, context, order_data):
    """معالجة الطلب الجديد"""
    try:
        user = update.effective_user
        logger.info(f"🔄 بدء معالجة طلب جديد من المستخدم: {user.id} - {user.first_name}")
        logger.info(f"📦 بيانات الطلب المستلمة: {order_data}")

        # التأكد من وجود المستخدم في قاعدة البيانات أولاً
        logger.info(f"👤 التحقق من وجود المستخدم في قاعدة البيانات...")
        await ensure_user_exists(user)

        # التأكد من وجود المنتجات وأنها قائمة
        items = order_data.get('items')
        if not items or not isinstance(items, list):
            logger.error("لا توجد منتجات في الطلب أو نوعها غير صحيح")
            await update.effective_message.reply_text("❌ خطأ: لا توجد منتجات في الطلب")
            return
        # حساب المجموع إذا لم يكن موجوداً
        total = order_data.get('total')
        if total is None:
            total = 0
            for item in items:
                try:
                    total += float(item.get('price', 0)) * int(item.get('quantity', 1))
                except Exception:
                    continue
            order_data['total'] = total
        if 'date' not in order_data:
            order_data['date'] = datetime.now().strftime('%Y-%m-%d %H:%M')

        # تجهيز بيانات التوصيل من customer object
        customer_info = order_data.get('customer', {})
        delivery_info = {
            'name': customer_info.get('name', ''),
            'phone': customer_info.get('phone', ''),
            'address': customer_info.get('address', ''),
            'notes': customer_info.get('notes', '')
        }
        delivery_json = json.dumps(delivery_info, ensure_ascii=False)

        # حفظ الطلب في قاعدة البيانات
        logger.info(f"💾 بدء حفظ الطلب في قاعدة البيانات...")
        try:
            conn = await get_db_connection()
            async with conn.cursor() as cur:
                # حفظ الطلب الرئيسي
                logger.info(f"📝 إدراج الطلب الرئيسي للمستخدم {user.id}")
                await cur.execute('''
                    INSERT INTO orders (user_id, items, total, status, payment_method, created_at, delivery_info, notes)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ''', (
                    user.id,
                    json.dumps(items, ensure_ascii=False),
                    order_data.get('total', 0),
                    'pending',
                    order_data.get('payment_method', 'cash'),
                    datetime.now(),
                    delivery_json,
                    order_data.get('notes', '')
                ))
                await cur.execute('SELECT LAST_INSERT_ID()')
                order_id = (await cur.fetchone())[0]
                logger.info(f"✅ تم إنشاء الطلب برقم: {order_id}")

                # حفظ تفاصيل الطلب
                for item in items:
                    await cur.execute('''
                        INSERT INTO order_items (order_id, product_id, quantity, price)
                        VALUES (%s, %s, %s, %s)
                    ''', (order_id, item.get('id'), item.get('quantity'), item.get('price')))

                    # تحديث المخزون
                    await cur.execute('''
                        UPDATE products
                        SET stock_quantity = stock_quantity - %s
                        WHERE id = %s AND stock_quantity >= %s
                    ''', (item.get('quantity'), item.get('id'), item.get('quantity')))

            conn.close()
            logger.info(f"تم إدخال الطلب في قاعدة البيانات برقم: {order_id}")
        except Exception as db_err:
            logger.error(f"خطأ في إدخال الطلب في قاعدة البيانات: {db_err}", exc_info=True)
            await update.effective_message.reply_text("❌ خطأ في حفظ الطلب في قاعدة البيانات")
            return
        # رسالة تأكيد للعميل
        customer_message = f"""
✅ <b>تم استلام طلبك بنجاح!</b>

🏷️ <b>رقم الطلب:</b> #{order_id}
📅 <b>التاريخ:</b> {order_data['date']}
💰 <b>المبلغ الإجمالي:</b> {order_data['total']} جنيه

📦 <b>المنتجات:</b>
"""
        for item in items:
            try:
                item_total = float(item.get('price', 0)) * int(item.get('quantity', 1))
                customer_message += f"• {item.get('name', 'منتج غير محدد')} × {item.get('quantity', 1)} = {item_total} جنيه\n"
            except Exception:
                continue
        if isinstance(delivery_info, dict):
            customer_message += f"\n📍 <b>معلومات التوصيل:</b>\n"
            customer_message += f"• الاسم: {delivery_info.get('name', 'غير محدد')}\n"
            customer_message += f"• الهاتف: {delivery_info.get('phone', 'غير محدد')}\n"
            customer_message += f"• العنوان: {delivery_info.get('address', 'غير محدد')}\n"
        customer_message += "\n⏳ سيتم مراجعة طلبك والتواصل معك قريباً!"
        keyboard = [
            [InlineKeyboardButton("📋 طلباتي", callback_data="my_orders")],
            [InlineKeyboardButton("🛍️ طلب جديد", web_app=WebAppInfo(url=WEB_APP_URL))]
        ]
        logger.info(f"📤 إرسال رسالة تأكيد للعميل {user.id}")
        await update.effective_message.reply_text(
            customer_message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML'
        )
        logger.info(f"✅ تم إرسال رسالة التأكيد بنجاح للعميل {user.id}")
        # جلب بيانات الطلب من قاعدة البيانات لإرسالها للمديرين
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            await cur.execute('SELECT * FROM orders WHERE id=%s', (order_id,))
            order_row = await cur.fetchone()
        conn.close()
        await notify_admins(context, order_row, user, order_data)
    except Exception as e:
        logger.error(f"خطأ في معالجة الطلب: {e}", exc_info=True)
        await update.effective_message.reply_text("❌ حدث خطأ في معالجة الطلب")

async def notify_admins(context, order_row, customer, order_data):
    """إرسال إشعار للمديرين"""
    try:
        order_id = order_row['id']
        admin_message = f"""
🔔 <b>طلب جديد وصل!</b>

🏷️ <b>رقم الطلب:</b> #{order_id}
👤 <b>العميل:</b> {customer.first_name}
🆔 <b>معرف العميل:</b> <code>{customer.id}</code>
👤 <b>اسم المستخدم:</b> @{customer.username or 'غير محدد'}
📅 <b>التاريخ:</b> {order_data.get('date', order_row['created_at'])}

📦 <b>المنتجات المطلوبة:</b>
"""
        total_items = 0
        items = order_data.get('items', [])
        for item in items:
            item_total = item.get('price', 0) * item.get('quantity', 1)
            total_items += item.get('quantity', 1)
            admin_message += f"• {item.get('name', 'منتج غير محدد')} × {item.get('quantity', 1)} = {item_total} جنيه\n"
        admin_message += f"\n📊 <b>إجمالي المنتجات:</b> {total_items} قطعة"
        admin_message += f"\n💰 <b>المبلغ الإجمالي:</b> {order_data.get('total', order_row['total'])} جنيه"

        # إضافة طريقة الدفع
        payment_method_text = {
            'cash': '💵 الدفع عند الاستلام',
            'card': '💳 بطاقة ائتمان',
            'bank': '🏦 تحويل بنكي',
            'wallet': '📱 محفظة إلكترونية'
        }.get(order_data.get('payment_method', 'cash'), '💵 الدفع عند الاستلام')
        admin_message += f"\n💳 <b>طريقة الدفع:</b> {payment_method_text}"

        if 'delivery_info' in order_data:
            delivery = order_data['delivery_info']
            admin_message += f"\n\n📍 <b>معلومات التوصيل:</b>\n"
            admin_message += f"• الاسم: {delivery.get('name', 'غير محدد')}\n"
            admin_message += f"• الهاتف: <code>{delivery.get('phone', 'غير محدد')}</code>\n"
            admin_message += f"• العنوان: {delivery.get('address', 'غير محدد')}\n"
            if delivery.get('notes'):
                admin_message += f"• ملاحظات: {delivery.get('notes')}\n"
        admin_keyboard = [
            [
                InlineKeyboardButton("✅ قبول الطلب", callback_data=f"accept_{order_id}"),
                InlineKeyboardButton("❌ رفض الطلب", callback_data=f"reject_{order_id}")
            ],
            [
                InlineKeyboardButton("🚚 جاري التوصيل", callback_data=f"shipping_{order_id}"),
                InlineKeyboardButton("📦 تم التوصيل", callback_data=f"delivered_{order_id}")
            ],
            [
                InlineKeyboardButton("📞 التواصل مع العميل", url=f"tg://user?id={customer.id}"),
                InlineKeyboardButton("📋 تفاصيل الطلب", callback_data=f"details_{order_id}")
            ]
        ]
        for admin_id in ADMIN_IDS:
            try:
                await context.bot.send_message(
                    admin_id,
                    admin_message,
                    reply_markup=InlineKeyboardMarkup(admin_keyboard),
                    parse_mode='HTML'
                )
                logger.info(f"تم إرسال إشعار للمدير: {admin_id}")
            except Exception as e:
                logger.error(f"فشل إرسال إشعار للمدير {admin_id}: {e}")
    except Exception as e:
        logger.error(f"خطأ في إشعار المديرين: {e}")

async def handle_callbacks(update: Update, context):
    """معالجة الأزرار"""
    query = update.callback_query
    await query.answer()
    
    data = query.data
    user_id = query.from_user.id
    
    try:
        if data == "my_orders":
            await show_user_orders(query, user_id)
        elif data == "admin_panel":
            await show_admin_panel(query)
        elif data.startswith("accept_"):
            order_id = int(data.replace("accept_", ""))
            await handle_order_action(query, context, order_id, "accepted")
        elif data.startswith("reject_"):
            order_id = int(data.replace("reject_", ""))
            await handle_order_action(query, context, order_id, "rejected")
        elif data.startswith("details_"):
            order_id = int(data.replace("details_", ""))
            await show_order_details(query, order_id)
        elif data.startswith("shipping_"):
            order_id = int(data.replace("shipping_", ""))
            await handle_order_action(query, context, order_id, "shipping")
        elif data.startswith("delivered_"):
            order_id = int(data.replace("delivered_", ""))
            await handle_order_action(query, context, order_id, "delivered")
        elif data == "manage_products":
            await show_products_management(query)
        elif data == "add_product":
            await query.edit_message_text("📝 لإضافة منتج جديد، أرسل البيانات بالتنسيق التالي:\n\n/add_product اسم_المنتج|الوصف|السعر|رقم_الفئة|الكمية\n\nمثال:\n/add_product هاتف جديد|هاتف ذكي متطور|1500|1|25")
        elif data == "view_categories":
            await show_categories(query)
        elif data == "view_products":
            await show_products(query)
        elif data.startswith("products_page_"):
            page = int(data.replace("products_page_", ""))
            await show_products(query, page)
        elif data == "new_orders":
            await show_new_orders(query)
        elif data == "all_orders":
            await show_all_orders(query)
        elif data == "shipping_orders":
            await show_orders_by_status(query, "shipping", "🚚 الطلبات قيد التوصيل")
        elif data == "delivered_orders":
            await show_orders_by_status(query, "delivered", "📦 الطلبات المكتملة")
        elif data == "detailed_stats":
            await show_detailed_statistics(query)
        elif data == "sales_reports":
            await show_sales_reports(query)
        elif data == "manage_customers":
            await show_customers_management(query)
        elif data == "reviews_management":
            await show_reviews_management(query)
        elif data == "notifications_center":
            await show_notifications_center(query)
        elif data.startswith("orders_page_"):
            page = int(data.replace("orders_page_", ""))
            await show_all_orders(query, page)
        elif data.startswith("order_status_"):
            parts = data.split("_")
            status = parts[2]
            page = int(parts[3]) if len(parts) > 3 else 0
            status_names = {
                "shipping": "🚚 الطلبات قيد التوصيل",
                "delivered": "📦 الطلبات المكتملة"
            }
            await show_orders_by_status(query, status, status_names.get(status, "الطلبات"), page)
        elif data.startswith("review_"):
            review_id = int(data.replace("review_", ""))
            await show_review_details(query, review_id)
    except Exception as e:
        logger.error(f"خطأ في معالجة الأزرار: {e}")
        await query.edit_message_text("❌ حدث خطأ في معالجة الطلب")

async def show_order_details(query, order_id):
    """عرض تفاصيل الطلب للمدير من قاعدة البيانات"""
    try:
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            await cur.execute('SELECT * FROM orders WHERE id=%s', (order_id,))
            order = await cur.fetchone()
            if not order:
                await query.edit_message_text("❌ الطلب غير موجود!")
                conn.close()
                return
            await cur.execute('SELECT * FROM users WHERE id=%s', (order['user_id'],))
            user = await cur.fetchone()
        conn.close()

        items = json.loads(order['items']) if order['items'] else []
        delivery_info = json.loads(order['delivery_info']) if order['delivery_info'] else {}

        details_message = f"""
📋 <b>تفاصيل الطلب #{order_id}</b>

👤 <b>معلومات العميل:</b>
• الاسم: {user['name'] if user else 'غير محدد'}
• المعرف: <code>{order['user_id']}</code>
• اسم المستخدم: @{user['username'] if user and user['username'] else 'غير محدد'}

📅 <b>معلومات الطلب:</b>
• تاريخ الإنشاء: {order['created_at']}
• الحالة: {order['status']}
• طريقة الدفع: {order.get('payment_method', 'غير محدد')}

📦 <b>المنتجات:</b>
"""
        for item in items:
            item_total = item.get('price', 0) * item.get('quantity', 1)
            details_message += f"• {item.get('name', 'منتج غير محدد')} × {item.get('quantity', 1)} = {item_total} جنيه\n"
        details_message += f"\n💰 <b>المجموع:</b> {order['total']} جنيه"

        # معلومات التوصيل
        if delivery_info:
            details_message += f"\n\n📍 <b>معلومات التوصيل:</b>\n"
            details_message += f"• الاسم: {delivery_info.get('name', 'غير محدد')}\n"
            details_message += f"• الهاتف: <code>{delivery_info.get('phone', 'غير محدد')}</code>\n"
            details_message += f"• العنوان: {delivery_info.get('address', 'غير محدد')}\n"
            if delivery_info.get('notes'):
                details_message += f"• ملاحظات: {delivery_info.get('notes')}\n"

        keyboard = [
            [InlineKeyboardButton("✅ قبول الطلب", callback_data=f"accept_{order_id}")],
            [InlineKeyboardButton("❌ رفض الطلب", callback_data=f"reject_{order_id}")],
            [InlineKeyboardButton("🚚 قيد التوصيل", callback_data=f"shipping_{order_id}")],
            [InlineKeyboardButton("📞 التواصل مع العميل", url=f"tg://user?id={order['user_id']}")],
            [InlineKeyboardButton("🔙 العودة للطلبات", callback_data="new_orders")]
        ]

        await query.edit_message_text(
            details_message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"خطأ في عرض تفاصيل الطلب: {e}")
        await query.edit_message_text(
            "❌ حدث خطأ في تحميل تفاصيل الطلب",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
            ])
        )

async def show_user_orders(query, user_id):
    """عرض طلبات المستخدم من قاعدة البيانات"""
    conn = await get_db_connection()
    async with conn.cursor(aiomysql.DictCursor) as cur:
        await cur.execute('SELECT * FROM orders WHERE user_id=%s ORDER BY id DESC', (user_id,))
        user_orders = await cur.fetchall()
    conn.close()
    if not user_orders:
        await query.edit_message_text(
            "📋 لا توجد طلبات حالياً\n🛍️ ابدأ التسوق الآن!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🛍️ فتح المتجر", web_app=WebAppInfo(url=WEB_APP_URL))]
            ])
        )
        return
    message = "📋 <b>طلباتك:</b>\n\n"
    status_emoji = {
        "pending": "⏳", "accepted": "✅", "rejected": "❌", "completed": "🎉",
        "shipping": "🚚", "delivered": "📦", "جديد": "⏳"
    }
    status_text = {
        "pending": "قيد المراجعة", "accepted": "مقبول", "rejected": "مرفوض",
        "completed": "مكتمل", "shipping": "جاري التوصيل", "delivered": "تم التوصيل", "جديد": "قيد المراجعة"
    }
    for order in user_orders:
        current_status = order.get('status', 'pending')
        message += f"{status_emoji.get(current_status, '⏳')} طلب #{order['id']}\n"
        message += f"💰 المبلغ: {order['total']} جنيه\n"
        message += f"📅 التاريخ: {order['created_at']}\n"
        message += f"🔖 الحالة: {status_text.get(current_status, 'قيد المراجعة')}\n\n"
    await query.edit_message_text(
        message,
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("🛍️ طلب جديد", web_app=WebAppInfo(url=WEB_APP_URL))]
        ]),
        parse_mode='HTML'
    )

async def handle_order_action(query, context, order_id, action):
    """تحديث حالة الطلب في قاعدة البيانات مع إشعارات محسنة"""
    try:
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            # جلب بيانات الطلب والعميل
            await cur.execute('''
                SELECT o.*, u.name as customer_name, u.username
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                WHERE o.id = %s
            ''', (order_id,))
            order = await cur.fetchone()

            if not order:
                await query.edit_message_text("❌ الطلب غير موجود!")
                conn.close()
                return

            old_status = order['status']
            await cur.execute('UPDATE orders SET status=%s WHERE id=%s', (action, order_id))
        conn.close()

        # رسائل الإشعار المحسنة للعميل
        customer_messages = {
            "accepted": f"""
✅ <b>تم قبول طلبك!</b>

🏷️ <b>رقم الطلب:</b> #{order_id}
💰 <b>المبلغ:</b> {order['total']} جنيه
📅 <b>تاريخ الطلب:</b> {order['created_at']}

🎉 تم قبول طلبك بنجاح! سيتم التواصل معك قريباً لترتيب التسليم.

📞 للاستفسار: يمكنك التواصل معنا في أي وقت
            """,
            "rejected": f"""
❌ <b>تم رفض طلبك</b>

🏷️ <b>رقم الطلب:</b> #{order_id}
💰 <b>المبلغ:</b> {order['total']} جنيه

😔 نعتذر، تم رفض طلبك لأسباب فنية.
يمكنك إنشاء طلب جديد أو التواصل معنا للاستفسار.

📞 للاستفسار والمساعدة: تواصل معنا
            """,
            "shipping": f"""
🚚 <b>طلبك في الطريق إليك!</b>

🏷️ <b>رقم الطلب:</b> #{order_id}
💰 <b>المبلغ:</b> {order['total']} جنيه

📦 تم شحن طلبك وهو في الطريق إليك الآن!
⏰ سيصل خلال 24-48 ساعة

📞 لتتبع الشحنة: تواصل معنا
            """,
            "delivered": f"""
📦 <b>تم تسليم طلبك بنجاح!</b>

🏷️ <b>رقم الطلب:</b> #{order_id}
💰 <b>المبلغ:</b> {order['total']} جنيه

🎉 تم تسليم طلبك بنجاح!
شكراً لك على التسوق معنا ونتطلع لخدمتك مرة أخرى.

⭐ نرجو تقييم تجربتك معنا
            """
        }

        # إشعار العميل مع أزرار تفاعلية
        try:
            customer_keyboard = []
            if action == "accepted":
                customer_keyboard = [
                    [InlineKeyboardButton("📋 طلباتي", callback_data="my_orders")],
                    [InlineKeyboardButton("🛍️ طلب جديد", web_app=WebAppInfo(url=WEB_APP_URL))]
                ]
            elif action == "rejected":
                customer_keyboard = [
                    [InlineKeyboardButton("🛍️ طلب جديد", web_app=WebAppInfo(url=WEB_APP_URL))],
                    [InlineKeyboardButton("📞 التواصل معنا", url="https://t.me/support")]
                ]
            elif action == "shipping":
                customer_keyboard = [
                    [InlineKeyboardButton("📋 طلباتي", callback_data="my_orders")],
                    [InlineKeyboardButton("📞 تتبع الشحنة", url="https://t.me/support")]
                ]
            elif action == "delivered":
                customer_keyboard = [
                    [InlineKeyboardButton("🛍️ طلب جديد", web_app=WebAppInfo(url=WEB_APP_URL))],
                    [InlineKeyboardButton("⭐ تقييم التجربة", url="https://t.me/feedback")]
                ]

            await context.bot.send_message(
                order['user_id'],
                customer_messages[action],
                reply_markup=InlineKeyboardMarkup(customer_keyboard) if customer_keyboard else None,
                parse_mode='HTML'
            )
            logger.info(f"تم إشعار العميل {order['user_id']} بحالة الطلب {order_id}")
        except Exception as e:
            logger.error(f"فشل إشعار العميل: {e}")

        # إشعار المديرين الآخرين بالتحديث
        admin_notification = f"""
🔄 <b>تحديث حالة طلب</b>

🏷️ <b>رقم الطلب:</b> #{order_id}
👤 <b>العميل:</b> {order.get('customer_name', 'غير محدد')}
💰 <b>المبلغ:</b> {order['total']} جنيه

📊 <b>تغيير الحالة:</b>
من: {old_status} ← إلى: {action}

⏰ <b>وقت التحديث:</b> {datetime.now().strftime('%H:%M - %d/%m/%Y')}
        """

        # إرسال إشعار للمديرين الآخرين
        for admin_id in ADMIN_IDS:
            if admin_id != query.from_user.id:  # لا نرسل للمدير الذي قام بالتحديث
                try:
                    await context.bot.send_message(
                        admin_id,
                        admin_notification,
                        parse_mode='HTML'
                    )
                except Exception as e:
                    logger.error(f"فشل إشعار المدير {admin_id}: {e}")

        # تحديث رسالة المدير الحالي
        action_texts = {
            "accepted": "✅ قُبل",
            "rejected": "❌ رُفض",
            "shipping": "🚚 جاري التوصيل",
            "delivered": "📦 تم التوصيل"
        }
        action_text = action_texts.get(action, action)

        success_message = f"""
✅ <b>تم تحديث الطلب بنجاح!</b>

🏷️ <b>رقم الطلب:</b> #{order_id}
👤 <b>العميل:</b> {order.get('customer_name', 'غير محدد')}
🔄 <b>الحالة الجديدة:</b> {action_text}
📧 <b>تم إشعار العميل:</b> ✅

⏰ <b>وقت التحديث:</b> {datetime.now().strftime('%H:%M - %d/%m/%Y')}
        """

        await query.edit_message_text(
            success_message,
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("📋 العودة للطلبات", callback_data="new_orders")],
                [InlineKeyboardButton("🏠 العودة للإدارة", callback_data="admin_panel")],
                [InlineKeyboardButton(f"📋 تفاصيل الطلب", callback_data=f"details_{order_id}")]
            ]),
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"خطأ في معالجة إجراء الطلب: {e}")
        await query.edit_message_text(
            "❌ حدث خطأ في معالجة الطلب",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
            ])
        )

async def show_products_management(query):
    """عرض إدارة المنتجات"""
    conn = await get_db_connection()
    async with conn.cursor(aiomysql.DictCursor) as cur:
        await cur.execute('SELECT COUNT(*) as total FROM products')
        total_products = (await cur.fetchone())['total']
        await cur.execute('SELECT COUNT(*) as total FROM categories')
        total_categories = (await cur.fetchone())['total']
        await cur.execute('SELECT COUNT(*) as total FROM products WHERE stock_quantity > 0')
        in_stock = (await cur.fetchone())['total']
        await cur.execute('SELECT COUNT(*) as total FROM products WHERE stock_quantity = 0')
        out_of_stock = (await cur.fetchone())['total']
    conn.close()

    message = f"""
📦 <b>إدارة المنتجات</b>

📊 <b>الإحصائيات:</b>
• إجمالي المنتجات: {total_products}
• إجمالي الفئات: {total_categories}
• المنتجات المتوفرة: {in_stock}
• المنتجات غير المتوفرة: {out_of_stock}

🕐 آخر تحديث: {datetime.now().strftime('%H:%M - %d/%m/%Y')}
    """

    keyboard = [
        [InlineKeyboardButton("➕ إضافة منتج", callback_data="add_product")],
        [InlineKeyboardButton("📋 عرض الفئات", callback_data="view_categories")],
        [InlineKeyboardButton("📦 عرض المنتجات", callback_data="view_products")],
        [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
    ]

    await query.edit_message_text(
        message,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML'
    )

async def show_categories(query):
    """عرض الفئات"""
    conn = await get_db_connection()
    async with conn.cursor(aiomysql.DictCursor) as cur:
        await cur.execute('SELECT * FROM categories ORDER BY id')
        categories = await cur.fetchall()
    conn.close()

    if not categories:
        message = "📂 لا توجد فئات حالياً"
    else:
        message = "📂 <b>الفئات المتاحة:</b>\n\n"
        for cat in categories:
            status = "✅" if cat['is_active'] else "❌"
            message += f"{status} <b>{cat['name']}</b> (ID: {cat['id']})\n"
            message += f"   📝 {cat['description']}\n\n"

    keyboard = [
        [InlineKeyboardButton("🔙 العودة لإدارة المنتجات", callback_data="manage_products")]
    ]

    await query.edit_message_text(
        message,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML'
    )

async def show_products(query, page=0):
    """عرض المنتجات مع التصفح"""
    limit = 5  # عدد المنتجات في كل صفحة
    offset = page * limit

    conn = await get_db_connection()
    async with conn.cursor(aiomysql.DictCursor) as cur:
        # جلب المنتجات مع معلومات الفئة
        await cur.execute('''
            SELECT p.*, c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            ORDER BY p.id DESC
            LIMIT %s OFFSET %s
        ''', (limit, offset))
        products = await cur.fetchall()

        # عدد المنتجات الإجمالي
        await cur.execute('SELECT COUNT(*) as total FROM products')
        total_products = (await cur.fetchone())['total']
    conn.close()

    if not products:
        message = "📦 لا توجد منتجات حالياً"
        keyboard = [[InlineKeyboardButton("🔙 العودة لإدارة المنتجات", callback_data="manage_products")]]
    else:
        message = f"📦 <b>المنتجات (صفحة {page + 1}):</b>\n\n"

        for product in products:
            status = "✅ متوفر" if product['stock_quantity'] > 0 else "❌ غير متوفر"
            message += f"🏷️ <b>{product['name']}</b> (ID: {product['id']})\n"
            message += f"📝 {product['description']}\n"
            message += f"💰 السعر: {product['price']} جنيه\n"
            message += f"📂 الفئة: {product['category_name'] or 'غير محدد'}\n"
            message += f"📊 الكمية: {product['stock_quantity']}\n"
            message += f"🔖 الحالة: {status}\n\n"

        # أزرار التصفح
        keyboard = []
        nav_buttons = []

        if page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"products_page_{page-1}"))

        if (page + 1) * limit < total_products:
            nav_buttons.append(InlineKeyboardButton("➡️ التالي", callback_data=f"products_page_{page+1}"))

        if nav_buttons:
            keyboard.append(nav_buttons)

        keyboard.append([InlineKeyboardButton("🔙 العودة لإدارة المنتجات", callback_data="manage_products")])

    await query.edit_message_text(
        message,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML'
    )

async def show_admin_panel(query):
    """لوحة الإدارة من قاعدة البيانات"""
    conn = await get_db_connection()
    async with conn.cursor(aiomysql.DictCursor) as cur:
        await cur.execute('SELECT COUNT(*) as total FROM orders')
        total_orders = (await cur.fetchone())['total']
        await cur.execute("SELECT COUNT(*) as cnt FROM orders WHERE status='pending' OR status='جديد'")
        pending_orders = (await cur.fetchone())['cnt']
        await cur.execute("SELECT COUNT(*) as cnt FROM orders WHERE status='accepted'")
        accepted_orders = (await cur.fetchone())['cnt']
        await cur.execute("SELECT COUNT(*) as cnt FROM orders WHERE status='rejected'")
        rejected_orders = (await cur.fetchone())['cnt']
        await cur.execute("SELECT COUNT(*) as cnt FROM orders WHERE status='shipping'")
        shipping_orders = (await cur.fetchone())['cnt']
        await cur.execute("SELECT COUNT(*) as cnt FROM orders WHERE status='delivered'")
        delivered_orders = (await cur.fetchone())['cnt']
        await cur.execute("SELECT SUM(total) as revenue FROM orders WHERE status IN ('accepted', 'delivered')")
        total_revenue = (await cur.fetchone())['revenue'] or 0
        await cur.execute('SELECT COUNT(*) as users FROM users')
        total_users = (await cur.fetchone())['users']
        await cur.execute('SELECT COUNT(*) as products FROM products')
        total_products = (await cur.fetchone())['products']
        # إحصائيات اليوم
        await cur.execute("SELECT COUNT(*) as cnt FROM orders WHERE DATE(created_at) = CURDATE()")
        today_orders = (await cur.fetchone())['cnt']
        await cur.execute("SELECT COALESCE(SUM(total), 0) as revenue FROM orders WHERE DATE(created_at) = CURDATE() AND status IN ('accepted', 'delivered')")
        today_revenue = (await cur.fetchone())['revenue']
    conn.close()

    message = f"""
⚙️ <b>لوحة الإدارة الرئيسية</b>

📊 <b>إحصائيات عامة:</b>
• إجمالي الطلبات: {total_orders}
• إجمالي المستخدمين: {total_users}
• إجمالي المنتجات: {total_products}
• إجمالي الإيرادات: {total_revenue} جنيه

📈 <b>إحصائيات اليوم:</b>
• طلبات اليوم: {today_orders}
• إيرادات اليوم: {today_revenue} جنيه

🔄 <b>حالة الطلبات:</b>
• ⏳ معلقة: {pending_orders}
• ✅ مقبولة: {accepted_orders}
• 🚚 قيد التوصيل: {shipping_orders}
• 📦 تم التوصيل: {delivered_orders}
• ❌ مرفوضة: {rejected_orders}

🕐 آخر تحديث: {datetime.now().strftime('%H:%M - %d/%m/%Y')}
    """
    keyboard = [
        [
            InlineKeyboardButton(f"📋 الطلبات الجديدة ({pending_orders})", callback_data="new_orders"),
            InlineKeyboardButton("🔄 جميع الطلبات", callback_data="all_orders")
        ],
        [
            InlineKeyboardButton("🚚 قيد التوصيل", callback_data="shipping_orders"),
            InlineKeyboardButton("📦 تم التوصيل", callback_data="delivered_orders")
        ],
        [
            InlineKeyboardButton("📦 إدارة المنتجات", callback_data="manage_products"),
            InlineKeyboardButton("👥 إدارة العملاء", callback_data="manage_customers")
        ],
        [
            InlineKeyboardButton("📊 إحصائيات تفصيلية", callback_data="detailed_stats"),
            InlineKeyboardButton("� تقارير المبيعات", callback_data="sales_reports")
        ],
        [
            InlineKeyboardButton("⭐ إدارة التقييمات", callback_data="reviews_management"),
            InlineKeyboardButton("🔔 مركز الإشعارات", callback_data="notifications_center")
        ],
        [
            InlineKeyboardButton("🛍️ فتح المتجر", web_app=WebAppInfo(url=WEB_APP_URL)),
            InlineKeyboardButton("� تحديث", callback_data="admin_panel")
        ]
    ]
    await query.edit_message_text(
        message,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML'
    )

def main():
    """تشغيل البوت"""
    # التحقق من الإعدادات
    if not TOKEN or TOKEN == "ضع_توكن_البوت_هنا":
        print("❌ يجب وضع توكن البوت الصحيح!")
        return
    
    if not WEB_APP_URL or WEB_APP_URL == "https://yourdomain.com/store.html":
        print("❌ يجب وضع رابط Web App الصحيح!")
        return
    
    print("🚀 بدء تشغيل البوت...")
    
    # إنشاء التطبيق
    app = Application.builder().token(TOKEN).build()
    
    # إضافة المعالجات (الترتيب مهم!)
    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("add_product", add_product_command))
    app.add_handler(MessageHandler(filters.StatusUpdate.WEB_APP_DATA, handle_web_app_data))
    app.add_handler(CallbackQueryHandler(handle_callbacks))
    # المعالج العام يجب أن يكون الأخير
    app.add_handler(MessageHandler(filters.ALL, handle_all_messages))
    
    print("✅ البوت يعمل الآن!")
    print(f"🌐 Web App URL: {WEB_APP_URL}")
    print(f"👥 عدد المديرين: {len(ADMIN_IDS)}")
    
    # تشغيل البوت
    app.run_polling(drop_pending_updates=True)

async def show_new_orders(query):
    """عرض الطلبات الجديدة"""
    try:
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            await cur.execute('''
                SELECT o.*, u.name as customer_name, u.username
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                WHERE o.status = "pending"
                ORDER BY o.created_at DESC
                LIMIT 10
            ''')
            orders = await cur.fetchall()
        conn.close()

        if not orders:
            await query.edit_message_text(
                "📋 لا توجد طلبات جديدة حالياً",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
                ])
            )
            return

        message = "📋 <b>الطلبات الجديدة:</b>\n\n"
        for order in orders:
            customer_name = order.get('customer_name', 'غير محدد')
            message += f"🆔 طلب #{order['id']}\n"
            message += f"👤 العميل: {customer_name}\n"
            message += f"💰 المبلغ: {order['total']} جنيه\n"
            message += f"📅 التاريخ: {order['created_at']}\n"
            message += "─────────────\n"

        keyboard = []
        for order in orders[:5]:  # عرض أول 5 طلبات فقط
            keyboard.append([
                InlineKeyboardButton(f"📋 طلب #{order['id']}", callback_data=f"details_{order['id']}")
            ])

        keyboard.append([InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")])

        await query.edit_message_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"خطأ في عرض الطلبات الجديدة: {e}")
        await query.edit_message_text(
            "❌ حدث خطأ في تحميل الطلبات",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
            ])
        )

async def show_detailed_statistics(query):
    """عرض إحصائيات تفصيلية"""
    conn = await get_db_connection()
    async with conn.cursor(aiomysql.DictCursor) as cur:
        # إحصائيات الطلبات
        await cur.execute('SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = CURDATE()')
        today_orders = (await cur.fetchone())['count']

        await cur.execute('SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)')
        week_orders = (await cur.fetchone())['count']

        await cur.execute('SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)')
        month_orders = (await cur.fetchone())['count']

        # إحصائيات المبيعات
        await cur.execute('SELECT COALESCE(SUM(total), 0) as sales FROM orders WHERE DATE(created_at) = CURDATE() AND status IN ("accepted", "delivered")')
        today_sales = (await cur.fetchone())['sales']

        await cur.execute('SELECT COALESCE(SUM(total), 0) as sales FROM orders WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND status IN ("accepted", "delivered")')
        week_sales = (await cur.fetchone())['sales']

        await cur.execute('SELECT COALESCE(SUM(total), 0) as sales FROM orders WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND status IN ("accepted", "delivered")')
        month_sales = (await cur.fetchone())['sales']

        # أكثر المنتجات مبيعاً
        await cur.execute('''
            SELECT p.name, SUM(oi.quantity) as total_sold
            FROM order_items oi
            JOIN products p ON oi.product_id = p.id
            JOIN orders o ON oi.order_id = o.id
            WHERE o.status IN ("accepted", "delivered")
            GROUP BY p.id, p.name
            ORDER BY total_sold DESC
            LIMIT 5
        ''')
        top_products = await cur.fetchall()

    conn.close()

    message = f"""
📊 <b>إحصائيات تفصيلية</b>

📋 <b>الطلبات:</b>
• اليوم: {today_orders}
• هذا الأسبوع: {week_orders}
• هذا الشهر: {month_orders}

💰 <b>المبيعات:</b>
• اليوم: {today_sales} جنيه
• هذا الأسبوع: {week_sales} جنيه
• هذا الشهر: {month_sales} جنيه

🏆 <b>أكثر المنتجات مبيعاً:</b>
"""

    for i, product in enumerate(top_products, 1):
        message += f"{i}. {product['name']} ({product['total_sold']} قطعة)\n"

    if not top_products:
        message += "لا توجد مبيعات حتى الآن\n"

    message += f"\n🕐 آخر تحديث: {datetime.now().strftime('%H:%M - %d/%m/%Y')}"

    keyboard = [
        [InlineKeyboardButton("📈 تحديث الإحصائيات", callback_data="detailed_stats")],
        [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
    ]

    await query.edit_message_text(
        message,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML'
    )

async def show_all_orders(query, page=0):
    """عرض جميع الطلبات مع التصفح"""
    limit = 5
    offset = page * limit

    try:
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            await cur.execute('''
                SELECT o.*, u.name as customer_name, u.username
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                ORDER BY o.created_at DESC
                LIMIT %s OFFSET %s
            ''', (limit, offset))
            orders = await cur.fetchall()

            await cur.execute('SELECT COUNT(*) as total FROM orders')
            total_orders = (await cur.fetchone())['total']
        conn.close()

        if not orders:
            await query.edit_message_text(
                "📋 لا توجد طلبات حالياً",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
                ])
            )
            return

        message = f"📋 <b>جميع الطلبات (صفحة {page + 1}):</b>\n\n"

        status_emoji = {
            "pending": "⏳", "accepted": "✅", "rejected": "❌",
            "shipping": "🚚", "delivered": "📦"
        }

        for order in orders:
            customer_name = order.get('customer_name', 'غير محدد')
            status = order.get('status', 'pending')
            emoji = status_emoji.get(status, '⏳')

            message += f"{emoji} <b>طلب #{order['id']}</b>\n"
            message += f"👤 العميل: {customer_name}\n"
            message += f"💰 المبلغ: {order['total']} جنيه\n"
            message += f"📅 التاريخ: {order['created_at']}\n"
            message += f"🔖 الحالة: {status}\n"
            message += "─────────────\n"

        # أزرار التصفح
        keyboard = []
        nav_buttons = []

        if page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"orders_page_{page-1}"))

        if (page + 1) * limit < total_orders:
            nav_buttons.append(InlineKeyboardButton("➡️ التالي", callback_data=f"orders_page_{page+1}"))

        if nav_buttons:
            keyboard.append(nav_buttons)

        # أزرار الطلبات
        for order in orders[:3]:  # عرض أول 3 طلبات فقط
            keyboard.append([
                InlineKeyboardButton(f"📋 طلب #{order['id']}", callback_data=f"details_{order['id']}")
            ])

        keyboard.append([InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")])

        await query.edit_message_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"خطأ في عرض جميع الطلبات: {e}")
        await query.edit_message_text(
            "❌ حدث خطأ في تحميل الطلبات",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
            ])
        )

async def show_orders_by_status(query, status, title, page=0):
    """عرض الطلبات حسب الحالة"""
    limit = 5
    offset = page * limit

    try:
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            await cur.execute('''
                SELECT o.*, u.name as customer_name, u.username
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                WHERE o.status = %s
                ORDER BY o.created_at DESC
                LIMIT %s OFFSET %s
            ''', (status, limit, offset))
            orders = await cur.fetchall()

            await cur.execute('SELECT COUNT(*) as total FROM orders WHERE status = %s', (status,))
            total_orders = (await cur.fetchone())['total']
        conn.close()

        if not orders:
            await query.edit_message_text(
                f"📋 لا توجد طلبات في حالة {title}",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
                ])
            )
            return

        message = f"📋 <b>{title} (صفحة {page + 1}):</b>\n\n"

        for order in orders:
            customer_name = order.get('customer_name', 'غير محدد')
            message += f"🆔 <b>طلب #{order['id']}</b>\n"
            message += f"👤 العميل: {customer_name}\n"
            message += f"💰 المبلغ: {order['total']} جنيه\n"
            message += f"📅 التاريخ: {order['created_at']}\n"
            message += "─────────────\n"

        # أزرار التصفح
        keyboard = []
        nav_buttons = []

        if page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"order_status_{status}_{page-1}"))

        if (page + 1) * limit < total_orders:
            nav_buttons.append(InlineKeyboardButton("➡️ التالي", callback_data=f"order_status_{status}_{page+1}"))

        if nav_buttons:
            keyboard.append(nav_buttons)

        # أزرار الطلبات
        for order in orders[:3]:
            keyboard.append([
                InlineKeyboardButton(f"📋 طلب #{order['id']}", callback_data=f"details_{order['id']}")
            ])

        keyboard.append([InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")])

        await query.edit_message_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"خطأ في عرض الطلبات حسب الحالة: {e}")
        await query.edit_message_text(
            "❌ حدث خطأ في تحميل الطلبات",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
            ])
        )

async def show_sales_reports(query):
    """عرض تقارير المبيعات"""
    try:
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            # مبيعات اليوم
            await cur.execute('''
                SELECT COUNT(*) as orders, COALESCE(SUM(total), 0) as revenue
                FROM orders
                WHERE DATE(created_at) = CURDATE() AND status IN ('accepted', 'delivered')
            ''')
            today_data = await cur.fetchone()

            # مبيعات الأسبوع
            await cur.execute('''
                SELECT COUNT(*) as orders, COALESCE(SUM(total), 0) as revenue
                FROM orders
                WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                AND status IN ('accepted', 'delivered')
            ''')
            week_data = await cur.fetchone()

            # مبيعات الشهر
            await cur.execute('''
                SELECT COUNT(*) as orders, COALESCE(SUM(total), 0) as revenue
                FROM orders
                WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                AND status IN ('accepted', 'delivered')
            ''')
            month_data = await cur.fetchone()

            # أفضل المنتجات مبيعاً
            await cur.execute('''
                SELECT p.name, SUM(oi.quantity) as total_sold, SUM(oi.quantity * oi.price) as revenue
                FROM order_items oi
                JOIN products p ON oi.product_id = p.id
                JOIN orders o ON oi.order_id = o.id
                WHERE o.status IN ('accepted', 'delivered')
                GROUP BY p.id, p.name
                ORDER BY total_sold DESC
                LIMIT 5
            ''')
            top_products = await cur.fetchall()

        conn.close()

        message = f"""
📈 <b>تقارير المبيعات</b>

📊 <b>مبيعات اليوم:</b>
• الطلبات: {today_data['orders']}
• الإيرادات: {today_data['revenue']} جنيه

📊 <b>مبيعات الأسبوع:</b>
• الطلبات: {week_data['orders']}
• الإيرادات: {week_data['revenue']} جنيه

📊 <b>مبيعات الشهر:</b>
• الطلبات: {month_data['orders']}
• الإيرادات: {month_data['revenue']} جنيه

🏆 <b>أفضل المنتجات مبيعاً:</b>
"""

        for i, product in enumerate(top_products, 1):
            message += f"{i}. {product['name']}\n"
            message += f"   📦 {product['total_sold']} قطعة - 💰 {product['revenue']} جنيه\n\n"

        if not top_products:
            message += "لا توجد مبيعات حتى الآن\n"

        message += f"\n🕐 آخر تحديث: {datetime.now().strftime('%H:%M - %d/%m/%Y')}"

        keyboard = [
            [InlineKeyboardButton("📈 تحديث التقرير", callback_data="sales_reports")],
            [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
        ]

        await query.edit_message_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"خطأ في عرض تقارير المبيعات: {e}")
        await query.edit_message_text(
            "❌ حدث خطأ في تحميل التقارير",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
            ])
        )

async def show_customers_management(query):
    """عرض إدارة العملاء"""
    conn = await get_db_connection()
    async with conn.cursor(aiomysql.DictCursor) as cur:
        await cur.execute('SELECT COUNT(*) as count FROM users')
        total_customers = (await cur.fetchone())['count']

        await cur.execute('''
            SELECT COUNT(DISTINCT user_id) as count
            FROM orders
            WHERE status IN ("accepted", "delivered")
        ''')
        active_customers = (await cur.fetchone())['count']

        await cur.execute('''
            SELECT u.name, u.username, u.id, COUNT(o.id) as order_count, COALESCE(SUM(o.total), 0) as total_spent
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id AND o.status IN ("accepted", "delivered")
            GROUP BY u.id
            ORDER BY total_spent DESC
            LIMIT 10
        ''')
        top_customers = await cur.fetchall()
    conn.close()

    message = f"""
👥 <b>إدارة العملاء</b>

📊 <b>إحصائيات العملاء:</b>
• إجمالي العملاء: {total_customers}
• العملاء النشطون: {active_customers}

🏆 <b>أفضل العملاء:</b>
"""

    for i, customer in enumerate(top_customers[:5], 1):
        name = customer['name'] or 'غير محدد'
        username = f"@{customer['username']}" if customer['username'] else 'بدون معرف'
        message += f"{i}. {name} ({username})\n"
        message += f"   📋 {customer['order_count']} طلب - 💰 {customer['total_spent']} جنيه\n\n"

    keyboard = [
        [InlineKeyboardButton("📊 تحديث الإحصائيات", callback_data="manage_customers")],
        [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
    ]

    await query.edit_message_text(
        message,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML'
    )

async def show_reviews_management(query):
    """عرض إدارة التقييمات"""
    try:
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            # إحصائيات التقييمات
            await cur.execute('SELECT COUNT(*) as total FROM reviews')
            total_reviews = (await cur.fetchone())['total']

            await cur.execute('SELECT AVG(rating) as avg_rating FROM reviews')
            avg_rating = (await cur.fetchone())['avg_rating'] or 0

            # أحدث التقييمات
            await cur.execute('''
                SELECT r.*, u.name as customer_name, p.name as product_name
                FROM reviews r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN products p ON r.product_id = p.id
                ORDER BY r.created_at DESC
                LIMIT 5
            ''')
            recent_reviews = await cur.fetchall()
        conn.close()

        message = f"""
⭐ <b>إدارة التقييمات</b>

📊 <b>إحصائيات التقييمات:</b>
• إجمالي التقييمات: {total_reviews}
• متوسط التقييم: {avg_rating:.1f}/5.0 ⭐

📝 <b>أحدث التقييمات:</b>
"""

        if recent_reviews:
            for review in recent_reviews:
                stars = "⭐" * review['rating']
                customer_name = review.get('customer_name', 'غير محدد')
                product_name = review.get('product_name', 'غير محدد')
                comment = review.get('comment', 'بدون تعليق')[:50]

                message += f"\n{stars} ({review['rating']}/5)\n"
                message += f"👤 {customer_name}\n"
                message += f"📦 {product_name}\n"
                message += f"💬 {comment}...\n"
                message += f"📅 {review['created_at']}\n"
                message += "─────────────\n"
        else:
            message += "\nلا توجد تقييمات حتى الآن\n"

        keyboard = [
            [InlineKeyboardButton("📊 تحديث الإحصائيات", callback_data="reviews_management")],
            [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
        ]

        await query.edit_message_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"خطأ في عرض إدارة التقييمات: {e}")
        await query.edit_message_text(
            "❌ حدث خطأ في تحميل التقييمات",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
            ])
        )

async def show_notifications_center(query):
    """عرض مركز الإشعارات"""
    try:
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            # إحصائيات الإشعارات
            await cur.execute('SELECT COUNT(*) as total FROM notifications')
            total_notifications = (await cur.fetchone())['total']

            await cur.execute('SELECT COUNT(*) as unread FROM notifications WHERE is_read = FALSE')
            unread_notifications = (await cur.fetchone())['unread']

            # أحدث الإشعارات
            await cur.execute('''
                SELECT n.*, u.name as user_name
                FROM notifications n
                LEFT JOIN users u ON n.user_id = u.id
                ORDER BY n.created_at DESC
                LIMIT 10
            ''')
            recent_notifications = await cur.fetchall()
        conn.close()

        message = f"""
🔔 <b>مركز الإشعارات</b>

📊 <b>إحصائيات الإشعارات:</b>
• إجمالي الإشعارات: {total_notifications}
• غير المقروءة: {unread_notifications}

📬 <b>أحدث الإشعارات:</b>
"""

        if recent_notifications:
            for notification in recent_notifications[:5]:
                status_icon = "📩" if not notification['is_read'] else "📧"
                user_name = notification.get('user_name', 'غير محدد')
                title = notification['title'][:30]

                message += f"\n{status_icon} <b>{title}</b>\n"
                message += f"👤 إلى: {user_name}\n"
                message += f"📅 {notification['created_at']}\n"
                message += "─────────────\n"
        else:
            message += "\nلا توجد إشعارات\n"

        keyboard = [
            [InlineKeyboardButton("📊 تحديث الإشعارات", callback_data="notifications_center")],
            [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
        ]

        await query.edit_message_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"خطأ في عرض مركز الإشعارات: {e}")
        await query.edit_message_text(
            "❌ حدث خطأ في تحميل الإشعارات",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 العودة للإدارة", callback_data="admin_panel")]
            ])
        )

async def show_review_details(query, review_id):
    """عرض تفاصيل تقييم محدد"""
    try:
        conn = await get_db_connection()
        async with conn.cursor(aiomysql.DictCursor) as cur:
            await cur.execute('''
                SELECT r.*, u.name as customer_name, u.username, p.name as product_name
                FROM reviews r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN products p ON r.product_id = p.id
                WHERE r.id = %s
            ''', (review_id,))
            review = await cur.fetchone()
        conn.close()

        if not review:
            await query.edit_message_text(
                "❌ التقييم غير موجود",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("🔙 العودة للتقييمات", callback_data="reviews_management")]
                ])
            )
            return

        stars = "⭐" * review['rating']
        customer_name = review.get('customer_name', 'غير محدد')
        username = f"@{review['username']}" if review.get('username') else 'بدون معرف'
        product_name = review.get('product_name', 'غير محدد')
        comment = review.get('comment', 'بدون تعليق')

        message = f"""
⭐ <b>تفاصيل التقييم</b>

🆔 <b>رقم التقييم:</b> #{review['id']}
{stars} <b>التقييم:</b> {review['rating']}/5

👤 <b>العميل:</b> {customer_name} ({username})
📦 <b>المنتج:</b> {product_name}
🛒 <b>رقم الطلب:</b> #{review['order_id']}

💬 <b>التعليق:</b>
{comment}

📅 <b>تاريخ التقييم:</b> {review['created_at']}
        """

        keyboard = [
            [InlineKeyboardButton("🔙 العودة للتقييمات", callback_data="reviews_management")],
            [InlineKeyboardButton("🏠 العودة للإدارة", callback_data="admin_panel")]
        ]

        await query.edit_message_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"خطأ في عرض تفاصيل التقييم: {e}")
        await query.edit_message_text(
            "❌ حدث خطأ في تحميل التقييم",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 العودة للتقييمات", callback_data="reviews_management")]
            ])
        )

if __name__ == '__main__':
    # بدء تشغيل قاعدة البيانات
    loop = asyncio.get_event_loop()
    loop.run_until_complete(init_db())

    main()